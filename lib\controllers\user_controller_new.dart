import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../models/user_model.dart';
import '../models/department_model.dart';
import '../services/api/user_api_service.dart';

/// متحكم المستخدمين المحدث للعمل مع ASP.NET Core API
class UserController extends GetxController {
  final UserApiService _userApiService = UserApiService();

  // قوائم المستخدمين والأقسام
  final RxList<User> _allUsers = <User>[].obs;
  final RxList<User> _activeUsers = <User>[].obs;
  final RxList<User> _onlineUsers = <User>[].obs;
  final RxList<Department> _departments = <Department>[].obs;

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxString _searchQuery = ''.obs;
  final Rx<UserRole?> _selectedRoleFilter = Rx<UserRole?>(null);
  final RxInt _selectedDepartmentFilter = 0.obs;
  final RxBool _showActiveOnly = true.obs;

  // Getters
  List<User> get allUsers => _allUsers;
  List<User> get activeUsers => _activeUsers;
  List<User> get onlineUsers => _onlineUsers;
  List<Department> get departments => _departments;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get searchQuery => _searchQuery.value;
  UserRole? get selectedRoleFilter => _selectedRoleFilter.value;
  int get selectedDepartmentFilter => _selectedDepartmentFilter.value;
  bool get showActiveOnly => _showActiveOnly.value;

  // للتوافق مع الكود الموجود
  List<User> get users => _allUsers;

  @override
  void onInit() {
    super.onInit();
    _initializeData();
  }

  /// تهيئة البيانات الأساسية
  Future<void> _initializeData() async {
    try {
      await Future.wait([
        loadAllDepartments(),
        loadAllUsers(),
      ]);
    } catch (e) {
      debugPrint('خطأ في تهيئة بيانات المستخدمين: $e');
    }
  }

  /// تحميل جميع المستخدمين
  Future<void> loadAllUsers() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final users = await _userApiService.getAllUsers();
      _allUsers.assignAll(users);
      _filterUsers();
      debugPrint('تم تحميل ${users.length} مستخدم');
    } catch (e) {
      _error.value = 'خطأ في تحميل المستخدمين: $e';
      debugPrint('خطأ في تحميل المستخدمين: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل المستخدمين النشطين
  Future<void> loadActiveUsers() async {
    try {
      final users = await _userApiService.getActiveUsers();
      _activeUsers.assignAll(users);
      debugPrint('تم تحميل ${users.length} مستخدم نشط');
    } catch (e) {
      debugPrint('خطأ في تحميل المستخدمين النشطين: $e');
    }
  }

  /// تحميل المستخدمين المتصلين
  Future<void> loadOnlineUsers() async {
    try {
      final users = await _userApiService.getOnlineUsers();
      _onlineUsers.assignAll(users);
      debugPrint('تم تحميل ${users.length} مستخدم متصل');
    } catch (e) {
      debugPrint('خطأ في تحميل المستخدمين المتصلين: $e');
    }
  }

  /// تحميل جميع الأقسام
  Future<void> loadAllDepartments() async {
    try {
      final departments = await _userApiService.getAllDepartments();
      _departments.assignAll(departments);
      debugPrint('تم تحميل ${departments.length} قسم');
    } catch (e) {
      debugPrint('خطأ في تحميل الأقسام: $e');
    }
  }

  /// إنشاء مستخدم جديد
  Future<bool> createUser(User user) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final newUser = await _userApiService.createUser(user);
      if (newUser != null) {
        _allUsers.add(newUser);
        _filterUsers();
        debugPrint('تم إنشاء المستخدم بنجاح: ${newUser.name}');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في إنشاء المستخدم: $e';
      debugPrint('خطأ في إنشاء المستخدم: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث مستخدم
  Future<bool> updateUser(User user) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final updatedUser = await _userApiService.updateUser(user);
      if (updatedUser != null) {
        final index = _allUsers.indexWhere((u) => u.id == user.id);
        if (index != -1) {
          _allUsers[index] = updatedUser;
          _filterUsers();
        }
        debugPrint('تم تحديث المستخدم بنجاح: ${updatedUser.name}');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في تحديث المستخدم: $e';
      debugPrint('خطأ في تحديث المستخدم: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف مستخدم
  Future<bool> deleteUser(int userId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final success = await _userApiService.deleteUser(userId);
      if (success) {
        _allUsers.removeWhere((user) => user.id == userId);
        _filterUsers();
        debugPrint('تم حذف المستخدم بنجاح');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في حذف المستخدم: $e';
      debugPrint('خطأ في حذف المستخدم: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// البحث عن المستخدمين
  void searchUsers(String query) {
    _searchQuery.value = query;
    _filterUsers();
  }

  /// تطبيق مرشح الدور
  void setRoleFilter(UserRole? role) {
    _selectedRoleFilter.value = role;
    _filterUsers();
  }

  /// تطبيق مرشح القسم
  void setDepartmentFilter(int departmentId) {
    _selectedDepartmentFilter.value = departmentId;
    _filterUsers();
  }

  /// تطبيق مرشح النشاط
  void setActiveFilter(bool showActiveOnly) {
    _showActiveOnly.value = showActiveOnly;
    _filterUsers();
  }

  /// تطبيق جميع المرشحات
  void _filterUsers() {
    var filteredUsers = List<User>.from(_allUsers);

    // تطبيق مرشح البحث
    if (_searchQuery.value.isNotEmpty) {
      filteredUsers = filteredUsers.where((user) =>
          user.name.toLowerCase().contains(_searchQuery.value.toLowerCase()) ||
          user.email.toLowerCase().contains(_searchQuery.value.toLowerCase()) ||
          (user.username?.toLowerCase().contains(_searchQuery.value.toLowerCase()) ?? false)
      ).toList();
    }

    // تطبيق مرشح الدور
    if (_selectedRoleFilter.value != null) {
      filteredUsers = filteredUsers.where((user) => 
          user.role == _selectedRoleFilter.value).toList();
    }

    // تطبيق مرشح القسم
    if (_selectedDepartmentFilter.value > 0) {
      filteredUsers = filteredUsers.where((user) => 
          user.departmentId == _selectedDepartmentFilter.value).toList();
    }

    // تطبيق مرشح النشاط
    if (_showActiveOnly.value) {
      filteredUsers = filteredUsers.where((user) => user.isActive).toList();
    }

    _activeUsers.assignAll(filteredUsers);
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _searchQuery.value = '';
    _selectedRoleFilter.value = null;
    _selectedDepartmentFilter.value = 0;
    _showActiveOnly.value = true;
    _filterUsers();
  }

  /// تحديث حالة نشاط المستخدم
  Future<bool> updateUserActiveStatus(int userId, bool isActive) async {
    try {
      final success = await _userApiService.updateUserActiveStatus(userId, isActive);
      if (success) {
        final index = _allUsers.indexWhere((u) => u.id == userId);
        if (index != -1) {
          _allUsers[index] = _allUsers[index].copyWith(isActive: isActive);
          _filterUsers();
        }
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في تحديث حالة النشاط: $e';
      return false;
    }
  }

  /// تحديث دور المستخدم
  Future<bool> updateUserRole(int userId, UserRole role) async {
    try {
      final success = await _userApiService.updateUserRole(userId, role);
      if (success) {
        final index = _allUsers.indexWhere((u) => u.id == userId);
        if (index != -1) {
          _allUsers[index] = _allUsers[index].copyWith(role: role);
          _filterUsers();
        }
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في تحديث دور المستخدم: $e';
      return false;
    }
  }

  /// تحديث قسم المستخدم
  Future<bool> updateUserDepartment(int userId, int? departmentId) async {
    try {
      final success = await _userApiService.updateUserDepartment(userId, departmentId);
      if (success) {
        final index = _allUsers.indexWhere((u) => u.id == userId);
        if (index != -1) {
          _allUsers[index] = _allUsers[index].copyWith(departmentId: departmentId);
          _filterUsers();
        }
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في تحديث قسم المستخدم: $e';
      return false;
    }
  }

  /// الحصول على مستخدم بواسطة المعرف
  User? getUserById(int id) {
    try {
      return _allUsers.firstWhere((user) => user.id == id);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على المستخدمين في قسم معين
  List<User> getUsersByDepartment(int departmentId) {
    return _allUsers.where((user) => user.departmentId == departmentId).toList();
  }

  /// الحصول على المستخدمين بدور معين
  List<User> getUsersByRole(UserRole role) {
    return _allUsers.where((user) => user.role == role).toList();
  }

  /// الحصول على إحصائيات المستخدمين
  Map<String, int> getUserStatistics() {
    final total = _allUsers.length;
    final active = _allUsers.where((user) => user.isActive).length;
    final online = _allUsers.where((user) => user.isOnline).length;
    final admins = _allUsers.where((user) => user.role.isAdmin).length;

    return {
      'total': total,
      'active': active,
      'online': online,
      'admins': admins,
    };
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  Future<void> refresh() async {
    await Future.wait([
      loadAllUsers(),
      loadActiveUsers(),
      loadOnlineUsers(),
    ]);
  }
}
