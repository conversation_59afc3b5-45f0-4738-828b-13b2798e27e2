/// تكوين التطبيق
class AppConfig {
  /// عنوان خادم API (ASP.NET Core) - محدث للتوافق مع المشروع
  static String apiUrl = 'http://localhost:5000/api';

  /// عنوان خادم WebSocket (Node.js)
  static String websocketUrl = 'ws://localhost:8080/ws';

  /// عنوان خادم API البديل للـ HTTPS - محدث للتوافق مع المشروع
  static String apiUrlHttps = 'https://localhost:7000/api';

  /// مفتاح API للخرائط
  static String mapsApiKey = '';

  /// حجم الصفحة الافتراضي
  static int defaultPageSize = 20;

  /// الحد الأقصى لحجم الملف (بالبايت)
  static int maxFileSize = 10 * 1024 * 1024; // 10 ميجابايت

  /// الوقت المستقطع للطلبات (بالثواني)
  static int requestTimeout = 30;

  /// ما إذا كان التطبيق في وضع التطوير
  static bool isDevelopmentMode = true;

  /// الحصول على عنوان API المناسب (HTTP أو HTTPS)
  static String getApiUrl({bool useHttps = false}) {
    return useHttps ? apiUrlHttps : apiUrl;
  }

  /// تهيئة التكوين
  static void initialize({
    String? apiUrl,
    String? apiUrlHttps,
    String? websocketUrl,
    String? mapsApiKey,
    int? defaultPageSize,
    int? maxFileSize,
    int? requestTimeout,
    bool? isDevelopmentMode,
  }) {
    if (apiUrl != null) AppConfig.apiUrl = apiUrl;
    if (apiUrlHttps != null) AppConfig.apiUrlHttps = apiUrlHttps;
    if (websocketUrl != null) AppConfig.websocketUrl = websocketUrl;
    if (mapsApiKey != null) AppConfig.mapsApiKey = mapsApiKey;
    if (defaultPageSize != null) AppConfig.defaultPageSize = defaultPageSize;
    if (maxFileSize != null) AppConfig.maxFileSize = maxFileSize;
    if (requestTimeout != null) AppConfig.requestTimeout = requestTimeout;
    if (isDevelopmentMode != null) AppConfig.isDevelopmentMode = isDevelopmentMode;
  }
}
