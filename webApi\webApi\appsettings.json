{"ConnectionStrings": {"DefaultConnection": "Data Source=.\\sqlexpress;Initial Catalog=databasetasks;Integrated Security=True;Encrypt=False;Trust Server Certificate=True"}, "Jwt": {"SecretKey": "TasksManagementSystemSecretKey2024!@#$%^&*()_+", "Issuer": "TasksManagementAPI", "Audience": "TasksManagementClient", "AccessTokenExpirationMinutes": 60, "RefreshTokenExpirationDays": 7}, "Cors": {"AllowedOrigins": ["http://localhost:8080", "https://localhost:8080", "http://localhost:3000", "https://localhost:3000", "http://127.0.0.1:8080", "https://127.0.0.1:8080"], "AllowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"], "AllowedHeaders": ["Content-Type", "Authorization", "X-Requested-With", "Accept", "Origin", "Access-Control-Request-Method", "Access-Control-Request-Headers"], "AllowCredentials": true, "PreflightMaxAge": 86400}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}