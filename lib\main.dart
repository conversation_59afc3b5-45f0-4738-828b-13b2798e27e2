import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter_application_2/screens/auth/login_screen.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
// تم إزالة get_storage - سيتم استخدام shared_preferences
import 'services/storage_service.dart';
import 'services/api/api_service.dart';
import 'controllers/auth_controller.dart';
// سيتم تهيئة المتحكمات لاحقاً عند الحاجة
// import 'controllers/task_controller.dart';
// import 'controllers/user_controller.dart';
import 'constants/app_theme.dart';
import 'controllers/theme_controller.dart';
import 'controllers/language_controller.dart';
// تم إزالة الخدمات غير المتوافقة مع API
// سيتم استخدام API services بدلاً من الخدمات المحلية
// تم إزالة مستودع لوحة المعلومات
// تم إزالة خدمات قاعدة البيانات والتهجيرات
import 'localization/app_translations.dart';
import 'routes/app_routes.dart';

void main() async {
  // تهيئة Flutter
  WidgetsFlutterBinding.ensureInitialized();

  // تم إزالة تهيئة قاعدة البيانات - جاهز للربط مع API
  debugPrint('التطبيق جاهز للربط مع API');

  // تهيئة خدمة التخزين المحلي
  try {
    final storageService = StorageService();
    await storageService.initialize();
    Get.put(storageService, permanent: true);
    debugPrint('تم تهيئة خدمة التخزين المحلي بنجاح');
  } catch (e) {
    debugPrint('خطأ في تهيئة خدمة التخزين المحلي: $e');
  }

  // تهيئة خدمة API
  try {
    final apiService = ApiService();
    await apiService.initialize();
    Get.put(apiService, permanent: true);
    debugPrint('تم تهيئة خدمة API بنجاح');
  } catch (e) {
    debugPrint('خطأ في تهيئة خدمة API: $e');
  }

  // معالجة خاصة لمنصة الويب
  if (kIsWeb) {
    debugPrint('تشغيل على منصة الويب - تطبيق إعدادات خاصة بالويب');
    await Future.delayed(const Duration(milliseconds: 500));
  }

  // تم إزالة تهيئة قاعدة البيانات - سيتم استخدام API بدلاً من ذلك
  debugPrint('تم تخطي تهيئة قاعدة البيانات - سيتم استخدام API');

  // تهيئة المتحكمات الأساسية
  try {
    // تهيئة متحكم المصادقة
    Get.put(AuthController(), permanent: true);

    // تهيئة المتحكمات الأساسية
    Get.put(ThemeController(), permanent: true);
    Get.put(LanguageController(), permanent: true);

    // تهيئة متحكمات API (سيتم تهيئتها لاحقاً عند الحاجة)
    // Get.put(TaskController(), permanent: true);
    // Get.put(UserController(), permanent: true);

    debugPrint('تم تهيئة المتحكمات الأساسية بنجاح');
  } catch (e) {
    debugPrint('خطأ في تهيئة المتحكمات: $e');
  }

  // تسجيل مسارات شاشات Syncfusion
  // SyncfusionScreens.registerRoutes();

  // Run the app
  runApp(const MyApp());
}

// تم حذف دالة initializeDatabase - سيتم استخدام API بدلاً من قاعدة البيانات المحلية

// تم حذف دالة _createDefaultUserIfNeeded - سيتم إدارة المستخدمين عبر API

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {

  @override
  Widget build(BuildContext context) {
    // الحصول على متحكم السمة
    ThemeController? themeController;

    try {
      themeController = Get.find<ThemeController>();
    } catch (e) {
      debugPrint('خطأ في العثور على متحكم السمة: $e');
      // استمر باستخدام القيم الافتراضية إذا لم يتم العثور على المتحكم
    }

    // إنشاء مراقب المسارات
    final routeObserver = Get.put(RouteObserver<PageRoute>());

    return GetMaterialApp(
      title: 'نظام إدارة المهام',
      debugShowCheckedModeBanner: false,

      // إضافة مراقب المسارات
      navigatorObservers: [routeObserver],

      // تم إزالة الروابط الأولية - سيتم تهيئة المتحكمات في main

      // Define routes
      getPages: AppRoutes.pages,

      // الترجمات
      translations: AppTranslations(),
      locale: const Locale('ar', ''),
      fallbackLocale: const Locale('ar', ''),

      // إعدادات السمة - استخدام قيم مباشرة بدلاً من الوصول إلى متغيرات تفاعلية
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: themeController != null
          ? (themeController.isSystemTheme.value
              ? ThemeMode.system
              : (themeController.isDarkMode.value
                  ? ThemeMode.dark
                  : ThemeMode.light))
          : ThemeMode.system,

      // دعم التوطين
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('ar', ''), // العربية
      ],

      // اتجاه النص من اليمين إلى اليسار
      textDirection: TextDirection.rtl,

      // تطبيق الاتجاه من اليمين إلى اليسار
      builder: (context, child) {
        return Directionality(
          textDirection: TextDirection.rtl,
          child: child!,
        );
      },

      // Bypass login and directly show the home screen
      home: const LoginScreen(),
    );
  }
}
