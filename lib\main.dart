import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/services.dart';
import 'package:flutter_application_2/screens/auth/login_screen.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'services/storage_service.dart';
import 'services/api/api_service.dart';
import 'controllers/auth_controller.dart';
import 'constants/app_theme.dart';
import 'controllers/theme_controller.dart';
import 'controllers/task_controller.dart';
import 'controllers/notification_controller.dart';
import 'controllers/language_controller.dart';
import 'controllers/user_controller.dart';
import 'controllers/message_controller.dart';
import 'controllers/unified_chat_controller.dart'; // إضافة استيراد وحدة تحكم المحادثات الموحدة
import 'controllers/admin_controller.dart';
import 'controllers/task_type_controller.dart';
import 'controllers/task_status_controller.dart';
import 'controllers/task_priority_controller.dart';
import 'controllers/department_controller.dart'; // إضافة استيراد وحدة تحكم الأقسام
import 'controllers/power_bi_controller.dart'; // إضافة استيراد وحدة تحكم باور بي آي
import 'controllers/archive_controller.dart'; // إضافة استيراد وحدة تحكم الأرشفة الإلكترونية
import 'controllers/report_controller.dart'; // إضافة استيراد وحدة تحكم التقارير الجديدة
import 'controllers/dashboard_controller.dart'; // إضافة استيراد وحدة تحكم لوحة المعلومات
import 'controllers/dashboard_layout_controller.dart'; // إضافة استيراد وحدة تحكم تخطيط لوحة المعلومات
import 'controllers/unified_search_controller.dart'; // إضافة استيراد وحدة تحكم البحث الموحد
// تم إزالة استيرادات قاعدة البيانات والخدمات المحلية
import 'services/calendar_service.dart'; // إضافة استيراد خدمة التقويم
import 'services/power_bi_analytics_service.dart'; // إضافة استيراد خدمة تحليلات باور بي آي
import 'controllers/calendar_controller.dart'; // إضافة استيراد وحدة تحكم التقويم
import 'services/pdf_report_service.dart'; // إضافة استيراد خدمة تقارير PDF
import 'services/app_storage_service.dart'; // إضافة استيراد خدمة تخزين ملفات التطبيق
// تم إزالة خدمة التزامن - ستتم إدارتها عبر API
import 'services/chart_export_service.dart'; // إضافة استيراد خدمة تصدير المخططات
import 'services/dashboard_service.dart'; // إضافة استيراد خدمة لوحة المعلومات
import 'services/archive_service.dart'; // إضافة استيراد خدمة الأرشفة الإلكترونية
// تم إزالة مستودع لوحة المعلومات
// تم إزالة خدمات قاعدة البيانات والتهجيرات
import 'localization/app_translations.dart';
import 'routes/app_routes.dart';
import 'utils/inherited_widget_helper.dart'; // إضافة استيراد مساعد الوصول الآمن إلى MediaQuery و Theme
import 'utils/keyboard_fix_binding.dart'; // إضافة استيراد معالج أحداث لوحة المفاتيح
// إضافة استيراد معالج أحداث الماوس
// إضافة استيراد مسارات شاشات Syncfusion
// إضافة استيراد مستودع المستندات النصية
// إضافة استيراد خدمة المستندات النصية
// إضافة استيراد وحدة تحكم المستندات النصية
import 'bindings/app_bindings.dart'; // إضافة استيراد رابط التطبيق الرئيسي

void main() async {
  // استخدام معالج لوحة المفاتيح المخصص بدلاً من WidgetsFlutterBinding الافتراضي
  KeyboardFixBinding.ensureInitialized();

  // تم إزالة تهيئة قاعدة البيانات - جاهز للربط مع API
  debugPrint('التطبيق جاهز للربط مع API');

  // تهيئة GetStorage لحفظ إعدادات السمة
  try {
    await GetStorage.init();
    Get.put(GetStorage());
    debugPrint('تم تهيئة GetStorage بنجاح');
  } catch (e) {
    debugPrint('خطأ في تهيئة GetStorage: $e');
  }

  // معالجة خاصة لمنصة الويب
  if (kIsWeb) {
    debugPrint('تشغيل على منصة الويب - تطبيق إعدادات خاصة بالويب');

    // إضافة تأخير صغير قبل التهيئة للتأكد من تحميل DOM بشكل صحيح
    await Future.delayed(const Duration(milliseconds: 500));
  }

  // تهيئة خدمة تخزين ملفات التطبيق أولاً
  try {
    final appStorageService = await AppStorageService().init();
    Get.put(appStorageService);
    debugPrint('تم تهيئة خدمة تخزين ملفات التطبيق بنجاح');
  } catch (e) {
    debugPrint('خطأ في تهيئة خدمة تخزين ملفات التطبيق: $e');
  }

  // تم إزالة تهيئة قاعدة البيانات - سيتم استخدام API بدلاً من ذلك
  debugPrint('تم تخطي تهيئة قاعدة البيانات - سيتم استخدام API');

  // Initialize controllers with error handling
  try {
    // تهيئة الروابط الأساسية للتطبيق
    AppBindings().dependencies();

    // تهيئة باقي المتحكمات والخدمات
    Get.put(ThemeController());
    Get.put(TaskController());
    Get.put(NotificationController());
    Get.put(LanguageController());
    Get.put(UserController());
    Get.put(MessageController());
    Get.put(UnifiedChatController());
    Get.put(AdminController());
    Get.put(TaskTypeController());
    Get.put(TaskStatusController());
    Get.put(TaskPriorityController());
    // سيتم إضافة خدمات API للأقسام والصلاحيات لاحقاً
    Get.put(DepartmentController()); // إضافة وحدة تحكم الأقسام

    // تهيئة خدمة التقويم ووحدة التحكم
    final calendarService = await CalendarService().init();
    Get.put(calendarService);
    Get.put(CalendarController());

    // تهيئة خدمة تقارير PDF
    final pdfReportService = await PdfReportService().init();
    Get.put(pdfReportService);

    // تهيئة خدمة تحليلات باور بي آي ووحدة التحكم
    Get.put(PowerBIAnalyticsService());
    Get.put(PowerBIController());

    // تهيئة خدمة تصدير المخططات
    final chartExportService = ChartExportService();
    Get.put(chartExportService);

    // تهيئة خدمة لوحة المعلومات
    final dashboardService = await DashboardService().init();
    Get.put(dashboardService);

    // تهيئة خدمة الأرشفة الإلكترونية ووحدة التحكم
    final archiveService = await ArchiveService().init();
    Get.put(archiveService);
    Get.put(ArchiveController());

    // تهيئة وحدة تحكم التقارير الجديدة
    Get.put(ReportController());

    // تهيئة وحدة تحكم لوحة المعلومات
    Get.put(DashboardController());

    // تهيئة وحدة تحكم تخطيط لوحة المعلومات
    Get.put(DashboardLayoutController());

    // تهيئة وحدة تحكم البحث الموحد
    Get.put(UnifiedSearchController());
  } catch (e) {
    debugPrint('Failed to initialize controllers: $e');
    // Continue with app initialization even if controllers fail
  }

  // تسجيل مسارات شاشات Syncfusion
  // SyncfusionScreens.registerRoutes();

  // Run the app
  runApp(const MyApp());
}

// تم حذف دالة initializeDatabase - سيتم استخدام API بدلاً من قاعدة البيانات المحلية

// تم حذف دالة _createDefaultUserIfNeeded - سيتم إدارة المستخدمين عبر API

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();

    // تسجيل معالج أحداث لوحة المفاتيح عند بدء التطبيق
    HardwareKeyboard.instance.addHandler(_handleKeyEvent);
  }

  @override
  void dispose() {
    // إلغاء تسجيل معالج أحداث لوحة المفاتيح عند إغلاق التطبيق
    HardwareKeyboard.instance.removeHandler(_handleKeyEvent);
    super.dispose();
  }

  // معالج مخصص لأحداث لوحة المفاتيح
  bool _handleKeyEvent(KeyEvent event) {
    // السماح بمعالجة جميع أحداث لوحة المفاتيح
    // لا نقوم بأي معالجة خاصة هنا لتجنب التداخل مع KeyboardFixBinding
    return false;
  }

  @override
  Widget build(BuildContext context) {
    // الحصول على متحكم السمة
    ThemeController? themeController;

    try {
      themeController = Get.find<ThemeController>();
    } catch (e) {
      debugPrint('خطأ في العثور على متحكم السمة: $e');
      // استمر باستخدام القيم الافتراضية إذا لم يتم العثور على المتحكم
    }

    // إنشاء مراقب المسارات
    final routeObserver = Get.put(RouteObserver<PageRoute>());

    return GetMaterialApp(
      title: 'نظام إدارة المهام',
      debugShowCheckedModeBanner: false,

      // إضافة مراقب المسارات
      navigatorObservers: [routeObserver],

      // تعيين الروابط الأولية
      initialBinding: AppBindings(),

      // Define routes
      getPages: AppRoutes.pages,

      // الترجمات
      translations: AppTranslations(),
      locale: const Locale('ar', ''),
      fallbackLocale: const Locale('ar', ''),

      // إعدادات السمة - استخدام قيم مباشرة بدلاً من الوصول إلى متغيرات تفاعلية
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: themeController != null
          ? (themeController.isSystemTheme.value
              ? ThemeMode.system
              : (themeController.isDarkMode.value
                  ? ThemeMode.dark
                  : ThemeMode.light))
          : ThemeMode.system,

      // دعم التوطين
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('ar', ''), // العربية
      ],

      // اتجاه النص من اليمين إلى اليسار
      textDirection: TextDirection.rtl,

      // تعطيل الوصول المباشر إلى متغيرات GetX في بناء التطبيق
      // تم تحديث هذا الجزء لاستخدام SafeInheritedWidgetBuilder لتجنب أخطاء الوصول إلى MediaQuery و Theme
      builder: (context, child) {
        // استخدام SafeInheritedWidgetBuilder لضمان الوصول الآمن إلى MediaQuery و Theme
        return SafeInheritedWidgetBuilder(
          builder: (context, mediaQuery, theme) {
            // تطبيق الاتجاه من اليمين إلى اليسار
            return Directionality(
              textDirection: TextDirection.rtl,
              child: child!,
            );
          },
        );
      },

      // Bypass login and directly show the home screen
      home: const LoginScreen(),
    );
  }
}
