import 'dart:io' if (dart.library.html) 'package:flutter_application_2/utils/web_file_stub.dart';
import 'package:flutter/foundation.dart' show debugPrint;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';



import '../controllers/auth_controller.dart';

/// وحدة تحكم الأرشفة الإلكترونية
///
/// تدير حالة وعمليات نظام الأرشفة الإلكترونية
class ArchiveController extends GetxController {
  final ArchiveService archiveService = Get.find<ArchiveService>();
  final AuthController _authController = Get.find<AuthController>();

  final Uuid _uuid = const Uuid();

  // حالة التحميل
  final RxBool isLoading = false.obs;
  final RxBool isUploading = false.obs;
  final RxDouble uploadProgress = 0.0.obs;

  // الوثائق
  final Rx<ArchiveDocument?> currentDocument = Rx<ArchiveDocument?>(null);
  final RxList<ArchiveDocument> documents = <ArchiveDocument>[].obs;
  final RxList<ArchiveDocument> recentDocuments = <ArchiveDocument>[].obs;

  // إصدارات الوثائق
  final RxList<ArchiveDocumentVersion> documentVersions = <ArchiveDocumentVersion>[].obs;
  final Rx<ArchiveDocumentVersion?> currentVersion = Rx<ArchiveDocumentVersion?>(null);

  // التصنيفات
  final RxList<ArchiveCategory> categories = <ArchiveCategory>[].obs;
  final RxList<Map<String, dynamic>> categoryTree = <Map<String, dynamic>>[].obs;
  final Rx<ArchiveCategory?> selectedCategory = Rx<ArchiveCategory?>(null);

  // الوسوم
  final RxList<ArchiveTag> tags = <ArchiveTag>[].obs;
  final RxList<ArchiveTag> selectedTags = <ArchiveTag>[].obs;

  // البحث والتصفية
  final RxString searchQuery = ''.obs;
  final Rx<ArchiveDocumentConfidentiality?> selectedConfidentiality = Rx<ArchiveDocumentConfidentiality?>(null);
  final Rx<ArchiveDocumentImportance?> selectedImportance = Rx<ArchiveDocumentImportance?>(null);
  final Rx<DateTime?> fromDate = Rx<DateTime?>(null);
  final Rx<DateTime?> toDate = Rx<DateTime?>(null);
  final RxString sortBy = 'createdAt'.obs;
  final RxBool sortAscending = false.obs;

  // الصفحات
  final RxInt currentPage = 0.obs;
  final RxInt itemsPerPage = 20.obs;
  final RxInt totalItems = 0.obs;

  @override
  void onInit() {
    super.onInit();
    _loadInitialData();
  }

  /// تحميل البيانات الأولية
  Future<void> _loadInitialData() async {
    isLoading.value = true;
    try {
      await Future.wait([
        loadCategories(),
        loadTags(),
        loadDocuments(),
        loadRecentDocuments(),
      ]);
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات الأولية: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// تحميل التصنيفات
  Future<void> loadCategories() async {
    try {
      categories.value = await archiveService.getAllCategories();
      categoryTree.value = await archiveService.getCategoryTree();
      update(); // Notify GetBuilder widgets to update
    } catch (e) {
      debugPrint('خطأ في تحميل التصنيفات: $e');
    }
  }

  /// تحميل الوسوم
  Future<void> loadTags() async {
    try {
      tags.value = await archiveService.getAllTags();
      update(); // Notify GetBuilder widgets to update
    } catch (e) {
      debugPrint('خطأ في تحميل الوسوم: $e');
    }
  }

  /// تحميل الوثائق
  Future<void> loadDocuments() async {
    isLoading.value = true;
    try {
      final List<String>? tagIds = selectedTags.isNotEmpty
          ? selectedTags.map((tag) => tag.id).toList()
          : null;

      documents.value = await archiveService.searchDocuments(
        searchQuery: searchQuery.value.isEmpty ? null : searchQuery.value,
        categoryId: selectedCategory.value?.id,
        tagIds: tagIds,
        confidentiality: selectedConfidentiality.value,
        importance: selectedImportance.value,
        fromDate: fromDate.value,
        toDate: toDate.value,
        orderBy: '${sortBy.value} ${sortAscending.value ? 'ASC' : 'DESC'}',
        limit: itemsPerPage.value,
        offset: currentPage.value * itemsPerPage.value,
      );
      update(); // Notify GetBuilder widgets to update
    } catch (e) {
      debugPrint('خطأ في تحميل الوثائق: $e');
    } finally {
      isLoading.value = false;
      update(); // Notify GetBuilder widgets about loading state change
    }
  }

  /// تحميل الوثائق الحديثة
  Future<void> loadRecentDocuments() async {
    try {
      recentDocuments.value = await archiveService.searchDocuments(
        orderBy: 'createdAt DESC',
        limit: 5,
      );
      update(); // Notify GetBuilder widgets to update
    } catch (e) {
      debugPrint('خطأ في تحميل الوثائق الحديثة: $e');
    }
  }

  /// تحميل وثيقة بواسطة المعرف
  Future<void> loadDocument(String id) async {
    isLoading.value = true;
    update();
    try {
      currentDocument.value = await archiveService.getDocumentById(id);
      update();
    } catch (e) {
      debugPrint('خطأ في تحميل الوثيقة: $e');
    } finally {
      isLoading.value = false;
      update();
    }
  }

  /// رفع وثيقة جديدة
  Future<ArchiveDocument?> uploadDocument({
    required String title,
    required String categoryId,
    required File file,
    required String fileName,
    required String fileType,
    String? description,
    List<String>? tagIds,
    String? documentNumber,
    DateTime? documentDate,
    String? issuer,
    String? recipient,
    ArchiveDocumentConfidentiality confidentiality = ArchiveDocumentConfidentiality.normal,
    ArchiveDocumentImportance importance = ArchiveDocumentImportance.normal,
    String? metadata,
  }) async {
    isUploading.value = true;
    uploadProgress.value = 0.0;
    update();
    try {
      // الحصول على معرف المستخدم الحالي
      final creatorId = _authController.currentUser.value?.id ?? '';
      if (creatorId.isEmpty) {
        throw Exception('لم يتم تسجيل الدخول');
      }

      // رفع الوثيقة
      final document = await archiveService.uploadDocument(
        title: title,
        categoryId: categoryId,
        creatorId: creatorId,
        file: file,
        fileName: fileName,
        fileType: fileType,
        description: description,
        tagIds: tagIds,
        documentNumber: documentNumber,
        documentDate: documentDate,
        issuer: issuer,
        recipient: recipient,
        confidentiality: confidentiality,
        importance: importance,
        metadata: metadata,
      );

      if (document != null) {
        // تحديث قائمة الوثائق
        await loadDocuments();
        await loadRecentDocuments();
      }

      return document;
    } catch (e) {
      debugPrint('خطأ في رفع الوثيقة: $e');
      return null;
    } finally {
      isUploading.value = false;
      uploadProgress.value = 0.0;
      update();
    }
  }

  /// إنشاء تصنيف جديد
  Future<ArchiveCategory?> createCategory({
    required String name,
    String? description,
    String? parentId,
    String? color,
    String? icon,
  }) async {
    try {
      final category = ArchiveCategory(
        id: _uuid.v4(),
        name: name,
        description: description,
        parentId: parentId,
        color: color ?? '#3498db',
        icon: icon,
        order: categories.length,
        createdAt: DateTime.now(),
      );

      final createdCategory = await archiveService.createCategory(category);
      if (createdCategory != null) {
        // تحديث قائمة التصنيفات
        await loadCategories();
      }

      return createdCategory;
    } catch (e) {
      debugPrint('خطأ في إنشاء التصنيف: $e');
      return null;
    }
  }

  /// إنشاء وسم جديد
  Future<ArchiveTag?> createTag({
    required String name,
    String? description,
    String? color,
  }) async {
    try {
      final tag = ArchiveTag(
        id: _uuid.v4(),
        name: name,
        description: description,
        color: color ?? '#3498db',
        createdAt: DateTime.now(),
      );

      final createdTag = await archiveService.createTag(tag);
      if (createdTag != null) {
        // تحديث قائمة الوسوم
        await loadTags();
      }

      return createdTag;
    } catch (e) {
      debugPrint('خطأ في إنشاء الوسم: $e');
      return null;
    }
  }

  /// تحديث وثيقة
  Future<bool> updateDocument(ArchiveDocument document, {
    bool createVersion = true,
    String? changeNotes,
  }) async {
    try {
      final success = await archiveService.updateDocument(
        document,
        createVersion: createVersion,
        changeNotes: changeNotes,
      );
      if (success) {
        // تحديث الوثيقة الحالية
        currentDocument.value = document;
        // تحديث قائمة الوثائق
        await loadDocuments();
        // تحميل إصدارات الوثيقة إذا كانت مطلوبة
        if (createVersion) {
          await loadDocumentVersions(document.id);
        }
      }
      return success;
    } catch (e) {
      debugPrint('خطأ في تحديث الوثيقة: $e');
      return false;
    }
  }

  /// حذف وثيقة
  Future<bool> deleteDocument(String id, {bool deleteVersions = true}) async {
    try {
      final success = await archiveService.deleteDocument(id, deleteVersions: deleteVersions);
      if (success) {
        // تحديث قائمة الوثائق
        await loadDocuments();
        // إذا كانت الوثيقة الحالية هي المحذوفة، قم بإعادة تعيينها
        if (currentDocument.value?.id == id) {
          currentDocument.value = null;
          // إعادة تعيين الإصدارات أيضًا
          documentVersions.clear();
          currentVersion.value = null;
        }
      }
      return success;
    } catch (e) {
      debugPrint('خطأ في حذف الوثيقة: $e');
      return false;
    }
  }

  /// تحميل إصدارات وثيقة
  Future<void> loadDocumentVersions(String documentId) async {
    isLoading.value = true;
    update();
    try {
      documentVersions.value = await archiveService.getVersionsByDocumentId(
        documentId,
        orderBy: 'versionNumber',
        descending: true,
      );
      update();
    } catch (e) {
      debugPrint('خطأ في تحميل إصدارات الوثيقة: $e');
    } finally {
      isLoading.value = false;
      update();
    }
  }

  /// تحميل إصدار وثيقة بواسطة المعرف
  Future<void> loadDocumentVersion(String versionId) async {
    isLoading.value = true;
    update();
    try {
      currentVersion.value = await archiveService.getVersionById(versionId);
      update();
    } catch (e) {
      debugPrint('خطأ في تحميل إصدار الوثيقة: $e');
    } finally {
      isLoading.value = false;
      update();
    }
  }

  /// إنشاء إصدار جديد من وثيقة
  Future<ArchiveDocumentVersion?> createDocumentVersion(
    ArchiveDocument document, {
    String? changeNotes,
  }) async {
    try {
      final version = await archiveService.createVersionFromDocument(
        document,
        changeNotes: changeNotes,
      );

      if (version != null) {
        // تحميل إصدارات الوثيقة
        await loadDocumentVersions(document.id);
      }

      return version;
    } catch (e) {
      debugPrint('خطأ في إنشاء إصدار جديد من الوثيقة: $e');
      return null;
    }
  }

  /// إنشاء إصدار جديد مع ملف جديد
  Future<ArchiveDocumentVersion?> createDocumentVersionWithNewFile({
    required ArchiveDocument document,
    required File file,
    required String fileName,
    required String fileType,
    String? changeNotes,
    String? title,
    String? description,
    String? categoryId,
    List<String>? tagIds,
    String? documentNumber,
    DateTime? documentDate,
    String? issuer,
    String? recipient,
    ArchiveDocumentConfidentiality? confidentiality,
    ArchiveDocumentImportance? importance,
    String? metadata,
  }) async {
    isUploading.value = true;
    uploadProgress.value = 0.0;
    update();
    try {
      final version = await archiveService.createVersionWithNewFile(
        document: document,
        file: file,
        fileName: fileName,
        fileType: fileType,
        changeNotes: changeNotes,
        title: title,
        description: description,
        categoryId: categoryId,
        tagIds: tagIds,
        documentNumber: documentNumber,
        documentDate: documentDate,
        issuer: issuer,
        recipient: recipient,
        confidentiality: confidentiality,
        importance: importance,
        metadata: metadata,
      );

      if (version != null) {
        // تحميل إصدارات الوثيقة
        await loadDocumentVersions(document.id);
      }

      return version;
    } catch (e) {
      debugPrint('خطأ في إنشاء إصدار جديد مع ملف جديد: $e');
      return null;
    } finally {
      isUploading.value = false;
      uploadProgress.value = 0.0;
      update();
    }
  }

  /// حذف إصدار وثيقة
  Future<bool> deleteDocumentVersion(String versionId) async {
    try {
      final success = await archiveService.deleteVersion(versionId);
      if (success) {
        // إذا كان الإصدار الحالي هو المحذوف، قم بإعادة تعيينه
        if (currentVersion.value?.id == versionId) {
          currentVersion.value = null;
        }

        // تحديث قائمة الإصدارات إذا كان هناك وثيقة حالية
        if (currentDocument.value != null) {
          await loadDocumentVersions(currentDocument.value!.id);
        }
      }
      return success;
    } catch (e) {
      debugPrint('خطأ في حذف إصدار الوثيقة: $e');
      return false;
    }
  }

  /// تعيين التصنيف المحدد
  void setSelectedCategory(ArchiveCategory? category) {
    selectedCategory.value = category;
    update();
    loadDocuments();
  }

  /// إضافة وسم إلى الوسوم المحددة
  void addSelectedTag(ArchiveTag tag) {
    if (!selectedTags.contains(tag)) {
      selectedTags.add(tag);
      update();
      loadDocuments();
    }
  }

  /// إزالة وسم من الوسوم المحددة
  void removeSelectedTag(ArchiveTag tag) {
    selectedTags.remove(tag);
    update();
    loadDocuments();
  }

  /// تعيين مستوى السرية المحدد
  void setSelectedConfidentiality(ArchiveDocumentConfidentiality? confidentiality) {
    selectedConfidentiality.value = confidentiality;
    update();
    loadDocuments();
  }

  /// تعيين مستوى الأهمية المحدد
  void setSelectedImportance(ArchiveDocumentImportance? importance) {
    selectedImportance.value = importance;
    update();
    loadDocuments();
  }

  /// تعيين نطاق التاريخ
  void setDateRange(DateTime? from, DateTime? to) {
    fromDate.value = from;
    toDate.value = to;
    update();
    loadDocuments();
  }

  /// تعيين ترتيب النتائج
  void setSorting(String field, bool ascending) {
    sortBy.value = field;
    sortAscending.value = ascending;
    update();
    loadDocuments();
  }

  /// تعيين استعلام البحث
  void setSearchQuery(String query) {
    searchQuery.value = query;
    update();
    loadDocuments();
  }

  /// الانتقال إلى صفحة
  void goToPage(int page) {
    if (page >= 0) {
      currentPage.value = page;
      update();
      loadDocuments();
    }
  }

  /// إعادة تعيين عوامل التصفية
  void resetFilters() {
    searchQuery.value = '';
    selectedCategory.value = null;
    selectedTags.clear();
    selectedConfidentiality.value = null;
    selectedImportance.value = null;
    fromDate.value = null;
    toDate.value = null;
    sortBy.value = 'createdAt';
    sortAscending.value = false;
    currentPage.value = 0;
    update();
    loadDocuments();
  }
}
