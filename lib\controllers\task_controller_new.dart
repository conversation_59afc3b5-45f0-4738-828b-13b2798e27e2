import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../models/task_model.dart';
import '../services/api/task_api_service.dart';
import 'auth_controller.dart';

/// متحكم المهام المحدث للعمل مع ASP.NET Core API
class TaskController extends GetxController {
  final TaskApiService _taskApiService = TaskApiService();

  // قوائم المهام
  final RxList<Task> _allTasks = <Task>[].obs;
  final RxList<Task> _myTasks = <Task>[].obs;
  final RxList<TaskStatus> _taskStatuses = <TaskStatus>[].obs;
  final RxList<TaskPriority> _taskPriorities = <TaskPriority>[].obs;
  final RxList<TaskType> _taskTypes = <TaskType>[].obs;

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // المرشحات
  final RxInt _selectedStatusFilter = 0.obs;
  final RxInt _selectedPriorityFilter = 0.obs;
  final RxInt _selectedTypeFilter = 0.obs;
  final RxString _searchQuery = ''.obs;

  // Getters
  List<Task> get allTasks => _allTasks;
  List<Task> get myTasks => _myTasks;
  List<TaskStatus> get taskStatuses => _taskStatuses;
  List<TaskPriority> get taskPriorities => _taskPriorities;
  List<TaskType> get taskTypes => _taskTypes;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  int get selectedStatusFilter => _selectedStatusFilter.value;
  int get selectedPriorityFilter => _selectedPriorityFilter.value;
  int get selectedTypeFilter => _selectedTypeFilter.value;
  String get searchQuery => _searchQuery.value;

  // للتوافق مع الكود الموجود
  List<Task> get tasks => _allTasks;

  @override
  void onInit() {
    super.onInit();
    _initializeData();
  }

  /// تهيئة البيانات الأساسية
  Future<void> _initializeData() async {
    try {
      await Future.wait([
        loadTaskStatuses(),
        loadTaskPriorities(),
        loadTaskTypes(),
      ]);
      await loadAllTasks();
    } catch (e) {
      debugPrint('خطأ في تهيئة بيانات المهام: $e');
    }
  }

  /// تحميل جميع المهام
  Future<void> loadAllTasks() async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final tasks = await _taskApiService.getAllTasks();
      _allTasks.assignAll(tasks);
      _filterMyTasks();
      debugPrint('تم تحميل ${tasks.length} مهمة');
    } catch (e) {
      _error.value = 'خطأ في تحميل المهام: $e';
      debugPrint('خطأ في تحميل المهام: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحميل مهام المستخدم الحالي
  void _filterMyTasks() {
    // TODO: الحصول على معرف المستخدم الحالي من AuthController
    // final currentUserId = Get.find<AuthController>().getCurrentUserId();
    // if (currentUserId != null) {
    //   _myTasks.assignAll(_allTasks.where((task) => task.assigneeId == currentUserId));
    // }
    
    // مؤقتاً، نعرض جميع المهام
    _myTasks.assignAll(_allTasks);
  }

  /// تحميل حالات المهام
  Future<void> loadTaskStatuses() async {
    try {
      final statuses = await _taskApiService.getTaskStatuses();
      _taskStatuses.assignAll(statuses);
      debugPrint('تم تحميل ${statuses.length} حالة مهمة');
    } catch (e) {
      debugPrint('خطأ في تحميل حالات المهام: $e');
    }
  }

  /// تحميل أولويات المهام
  Future<void> loadTaskPriorities() async {
    try {
      final priorities = await _taskApiService.getTaskPriorities();
      _taskPriorities.assignAll(priorities);
      debugPrint('تم تحميل ${priorities.length} أولوية مهمة');
    } catch (e) {
      debugPrint('خطأ في تحميل أولويات المهام: $e');
    }
  }

  /// تحميل أنواع المهام
  Future<void> loadTaskTypes() async {
    try {
      final types = await _taskApiService.getTaskTypes();
      _taskTypes.assignAll(types);
      debugPrint('تم تحميل ${types.length} نوع مهمة');
    } catch (e) {
      debugPrint('خطأ في تحميل أنواع المهام: $e');
    }
  }

  /// إنشاء مهمة جديدة
  Future<bool> createTask(Task task) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final newTask = await _taskApiService.createTask(task);
      if (newTask != null) {
        _allTasks.add(newTask);
        _filterMyTasks();
        debugPrint('تم إنشاء المهمة بنجاح: ${newTask.title}');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في إنشاء المهمة: $e';
      debugPrint('خطأ في إنشاء المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث مهمة
  Future<bool> updateTask(Task task) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final updatedTask = await _taskApiService.updateTask(task);
      if (updatedTask != null) {
        final index = _allTasks.indexWhere((t) => t.id == task.id);
        if (index != -1) {
          _allTasks[index] = updatedTask;
          _filterMyTasks();
        }
        debugPrint('تم تحديث المهمة بنجاح: ${updatedTask.title}');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في تحديث المهمة: $e';
      debugPrint('خطأ في تحديث المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف مهمة
  Future<bool> deleteTask(int taskId) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final success = await _taskApiService.deleteTask(taskId);
      if (success) {
        _allTasks.removeWhere((task) => task.id == taskId);
        _filterMyTasks();
        debugPrint('تم حذف المهمة بنجاح');
        return true;
      }
      return false;
    } catch (e) {
      _error.value = 'خطأ في حذف المهمة: $e';
      debugPrint('خطأ في حذف المهمة: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// البحث في المهام
  void searchTasks(String query) {
    _searchQuery.value = query;
    _applyFilters();
  }

  /// تطبيق مرشح الحالة
  void setStatusFilter(int statusId) {
    _selectedStatusFilter.value = statusId;
    _applyFilters();
  }

  /// تطبيق مرشح الأولوية
  void setPriorityFilter(int priorityId) {
    _selectedPriorityFilter.value = priorityId;
    _applyFilters();
  }

  /// تطبيق مرشح النوع
  void setTypeFilter(int typeId) {
    _selectedTypeFilter.value = typeId;
    _applyFilters();
  }

  /// تطبيق جميع المرشحات
  void _applyFilters() {
    var filteredTasks = List<Task>.from(_allTasks);

    // تطبيق مرشح البحث
    if (_searchQuery.value.isNotEmpty) {
      filteredTasks = filteredTasks.where((task) =>
          task.title.toLowerCase().contains(_searchQuery.value.toLowerCase()) ||
          (task.description?.toLowerCase().contains(_searchQuery.value.toLowerCase()) ?? false)
      ).toList();
    }

    // تطبيق مرشح الحالة
    if (_selectedStatusFilter.value > 0) {
      filteredTasks = filteredTasks.where((task) => 
          task.status == _selectedStatusFilter.value).toList();
    }

    // تطبيق مرشح الأولوية
    if (_selectedPriorityFilter.value > 0) {
      filteredTasks = filteredTasks.where((task) => 
          task.priority == _selectedPriorityFilter.value).toList();
    }

    // تطبيق مرشح النوع
    if (_selectedTypeFilter.value > 0) {
      filteredTasks = filteredTasks.where((task) => 
          task.taskTypeId == _selectedTypeFilter.value).toList();
    }

    _myTasks.assignAll(filteredTasks);
  }

  /// مسح جميع المرشحات
  void clearFilters() {
    _selectedStatusFilter.value = 0;
    _selectedPriorityFilter.value = 0;
    _selectedTypeFilter.value = 0;
    _searchQuery.value = '';
    _filterMyTasks();
  }

  /// تحديث حالة المهمة
  Future<bool> updateTaskStatus(int taskId, int newStatusId) async {
    try {
      final task = _allTasks.firstWhere((t) => t.id == taskId);
      final updatedTask = task.copyWith(status: newStatusId);
      return await updateTask(updatedTask);
    } catch (e) {
      _error.value = 'خطأ في تحديث حالة المهمة: $e';
      return false;
    }
  }

  /// تحديث نسبة الإنجاز
  Future<bool> updateTaskProgress(int taskId, int progress) async {
    try {
      final task = _allTasks.firstWhere((t) => t.id == taskId);
      final updatedTask = task.copyWith(
        completionPercentage: progress,
        completedAt: progress >= 100 
            ? DateTime.now().millisecondsSinceEpoch ~/ 1000 
            : null,
      );
      return await updateTask(updatedTask);
    } catch (e) {
      _error.value = 'خطأ في تحديث نسبة الإنجاز: $e';
      return false;
    }
  }

  /// الحصول على إحصائيات المهام
  Map<String, int> getTaskStatistics() {
    final total = _allTasks.length;
    final completed = _allTasks.where((task) => task.isCompleted).length;
    final overdue = _allTasks.where((task) => task.isOverdue).length;
    final inProgress = total - completed;

    return {
      'total': total,
      'completed': completed,
      'inProgress': inProgress,
      'overdue': overdue,
    };
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }

  /// تحديث البيانات
  Future<void> refresh() async {
    await loadAllTasks();
  }
}
