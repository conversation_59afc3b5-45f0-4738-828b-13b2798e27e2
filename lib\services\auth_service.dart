import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../models/auth_models.dart';
import '../models/user_model.dart';
import 'api/api_service.dart';
import 'storage_service.dart';

/// خدمة المصادقة المحدثة للعمل مع ASP.NET Core API
class AuthService {
  final ApiService _apiService = ApiService();
  final StorageService _storageService = Get.find<StorageService>();

  // خصائص للوصول السريع إلى المستخدم الحالي وحالة المصادقة
  final Rx<UserInfo?> _currentUser = Rx<UserInfo?>(null);
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;

  // الحصول على المستخدم الحالي
  UserInfo? get currentUser => _currentUser.value;

  // التحقق من حالة التحميل
  bool get isLoading => _isLoading.value;

  // الحصول على رسالة الخطأ
  String get error => _error.value;

  // التحقق من حالة تسجيل الدخول
  bool get isLoggedIn => _currentUser.value != null && _apiService.isLoggedIn;

  // التحقق مما إذا كان المستخدم مديراً
  bool get isAdmin => _currentUser.value?.role.isAdmin ?? false;

  // التحقق مما إذا كان المستخدم مدير نظام
  bool get isSuperAdmin => _currentUser.value?.role.isSuperAdmin ?? false;

  // تهيئة الخدمة
  Future<void> initialize() async {
    try {
      await _apiService.initialize();
      await _loadCurrentUserFromStorage();
      debugPrint('تم تهيئة خدمة المصادقة');
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمة المصادقة: $e');
    }
  }

  // تحميل المستخدم الحالي من التخزين المحلي
  Future<void> _loadCurrentUserFromStorage() async {
    try {
      final sessionData = await _storageService.getSessionData();
      if (sessionData != null && !sessionData.isExpired) {
        _currentUser.value = sessionData.user;
        debugPrint('تم تحميل المستخدم الحالي من التخزين المحلي');
      }
    } catch (e) {
      debugPrint('خطأ في تحميل المستخدم الحالي: $e');
    }
  }

  /// تسجيل مستخدم جديد عبر API
  Future<AuthResponse> register(RegisterRequest request) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final response = await _apiService.post(
        '/auth/register',
        body: request.toJson(),
        requireAuth: false,
      );

      final authResponse = _apiService.handleResponse<AuthResponse>(
        response,
        (json) => AuthResponse.fromJson(json),
      );

      if (authResponse.isValid) {
        await _apiService.saveAuthResponse(authResponse);
        _currentUser.value = authResponse.user;
        debugPrint('تم تسجيل المستخدم بنجاح');
      }

      return authResponse;
    } catch (e) {
      _error.value = e.toString();
      debugPrint('خطأ في تسجيل المستخدم: $e');
      return AuthResponse(
        success: false,
        message: e.toString(),
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// تسجيل الدخول عبر API
  Future<AuthResponse> login(String emailOrUsername, String password) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final request = LoginRequest(
        usernameOrEmail: emailOrUsername,
        password: password,
      );

      final response = await _apiService.post(
        '/auth/login',
        body: request.toJson(),
        requireAuth: false,
      );

      final authResponse = _apiService.handleResponse<AuthResponse>(
        response,
        (json) => AuthResponse.fromJson(json),
      );

      if (authResponse.isValid) {
        await _apiService.saveAuthResponse(authResponse);
        _currentUser.value = authResponse.user;
        debugPrint('تم تسجيل الدخول بنجاح');
      }

      return authResponse;
    } catch (e) {
      _error.value = e.toString();
      debugPrint('خطأ في تسجيل الدخول: $e');
      return AuthResponse(
        success: false,
        message: e.toString(),
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// تسجيل الخروج
  Future<bool> logout() async {
    _isLoading.value = true;

    try {
      // محاولة إرسال طلب تسجيل الخروج إلى الخادم
      try {
        await _apiService.post('/auth/logout');
      } catch (e) {
        // تجاهل أخطاء الخادم في تسجيل الخروج
        debugPrint('تحذير: فشل في إرسال طلب تسجيل الخروج إلى الخادم: $e');
      }

      // مسح البيانات المحلية
      await _apiService.clearTokens();
      _currentUser.value = null;
      _error.value = '';

      debugPrint('تم تسجيل الخروج بنجاح');
      return true;
    } catch (e) {
      _error.value = e.toString();
      debugPrint('خطأ في تسجيل الخروج: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// الحصول على معرف المستخدم الحالي
  int? getCurrentUserId() {
    return _currentUser.value?.id;
  }

  /// الحصول على دور المستخدم الحالي
  UserRole? getCurrentUserRole() {
    return _currentUser.value?.role;
  }

  /// التحقق من كون المستخدم الحالي مديراً
  bool isCurrentUserAdmin() {
    return _currentUser.value?.role.isAdmin ?? false;
  }

  /// التحقق من كون المستخدم الحالي مدير قسم أو أعلى
  bool isCurrentUserManagerOrAbove() {
    return _currentUser.value?.role.isManagerOrAbove ?? false;
  }

  /// تحديث ملف المستخدم عبر API
  Future<UserInfo?> updateUserProfile(Map<String, dynamic> userData) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final userId = getCurrentUserId();
      if (userId == null) {
        throw Exception('لا يوجد مستخدم مسجل دخول');
      }

      final response = await _apiService.put(
        '/users/$userId',
        body: userData,
      );

      final updatedUser = _apiService.handleResponse<UserInfo>(
        response,
        (json) => UserInfo.fromJson(json),
      );

      _currentUser.value = updatedUser;
      debugPrint('تم تحديث ملف المستخدم بنجاح');
      return updatedUser;
    } catch (e) {
      _error.value = e.toString();
      debugPrint('خطأ في تحديث ملف المستخدم: $e');
      return null;
    } finally {
      _isLoading.value = false;
    }
  }

  /// تغيير كلمة المرور عبر API
  Future<bool> changePassword(String currentPassword, String newPassword) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final request = ChangePasswordRequest(
        currentPassword: currentPassword,
        newPassword: newPassword,
        confirmPassword: newPassword,
      );

      final response = await _apiService.post(
        '/auth/change-password',
        body: request.toJson(),
      );

      final authResponse = _apiService.handleResponse<AuthResponse>(
        response,
        (json) => AuthResponse.fromJson(json),
      );

      if (authResponse.success) {
        debugPrint('تم تغيير كلمة المرور بنجاح');
        return true;
      } else {
        _error.value = authResponse.message;
        return false;
      }
    } catch (e) {
      _error.value = e.toString();
      debugPrint('خطأ في تغيير كلمة المرور: $e');
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// التحقق من صحة الرمز
  Future<UserInfo?> validateToken() async {
    try {
      final response = await _apiService.get('/auth/validate-token');

      final userInfo = _apiService.handleResponse<UserInfo>(
        response,
        (json) => UserInfo.fromJson(json),
      );

      _currentUser.value = userInfo;
      return userInfo;
    } catch (e) {
      debugPrint('فشل في التحقق من صحة الرمز: $e');
      await logout();
      return null;
    }
  }

  /// نسيان كلمة المرور
  Future<AuthResponse> forgotPassword(String email) async {
    _isLoading.value = true;
    _error.value = '';

    try {
      final request = ForgotPasswordRequest(email: email);

      final response = await _apiService.post(
        '/auth/forgot-password',
        body: request.toJson(),
        requireAuth: false,
      );

      final authResponse = _apiService.handleResponse<AuthResponse>(
        response,
        (json) => AuthResponse.fromJson(json),
      );

      return authResponse;
    } catch (e) {
      _error.value = e.toString();
      debugPrint('خطأ في نسيان كلمة المرور: $e');
      return AuthResponse(
        success: false,
        message: e.toString(),
      );
    } finally {
      _isLoading.value = false;
    }
  }

  /// مسح رسالة الخطأ
  void clearError() {
    _error.value = '';
  }
}
